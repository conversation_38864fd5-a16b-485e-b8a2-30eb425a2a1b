<template>
  <div class="w-full p-0">
    <Form class="space-y-6 relative" :style="{ height: `${(djInfo?.qtHeight || 50) * sc}px` }">
      <div class="relative">
        <div
          v-for="item in items"
          :key="item.fieldName"
          class="absolute"
          :style="{
            left: `${item.posX * sc}px`,
            top: `${item.posY * sc}px`,
            width: `${item.fieldWidth * sc}px`,
            height: `${item.fieldHeight * sc}px`,
            fontSize: `${item.fieldFontSize * sc}px`,
            fontFamily: item.fieldFontName,
            backgroundColor: item.backColor === 0 ? 'transparent' : colorToRgba(item.backColor),
          }"
        >
          <template v-if="item.lx === 'A'">
            <span :class="parseStyle(item)">{{ item.labTitle }} </span>
          </template>
          <template v-if="item.lx === 'S'">
            <span :class="parseStyle(item)">{{ sum(item, 'a') }} </span>
          </template>
          <template v-if="item.lx === 'P'">
            <span :class="parseStyle(item)">{{ sum(item, 'p') }} </span>
          </template>
          <FormItem :name="item.labTitle" v-if="item.fldType === 'B'">
            <a-input
              :disabled="!item.canEdit"
              v-model:value="localFormData[item.fieldName]"
              class="p-.4 rounded-none"
              :style="`
                color: ${item.fieldFontColor === 0 ? 'black' : colorToRgba(item.fieldFontColor)};
                font-size: ${item.fieldFontSize * sc}px;
                font-family: ${item.fieldFontName};
                z-index: 10;
                ${item.field === 'Y' ? 'border: 0px; border-bottom: 1px solid #000;' : ''}
              `"
            />
          </FormItem>
        </div>

        <template v-if="djInfo && djInfo.lineList">
          <div
            v-for="item in djInfo.lineList.filter((line) => !line.upMx)"
            :key="item.field"
            class="absolute"
            :style="{
              left: `${item.sposX * sc}px`,
              top: `${item.sposY * sc}px`,
              width: `${item.lrbWidth * sc}px`,
              height: `${item.lrbHeight * sc}px`,
            }"
          >
            <template v-if="item.lx === 'H' || item.lx === 'V'">
              <div
                :style="{
                  position: 'absolute',
                  backgroundColor: item.lineColor === 0 ? 'black' : item.lineColor,
                  width: item.lx === 'H' ? `${item.dposX * sc}px` : `${item.lineWidth * sc}px`,
                  height: item.lx === 'V' ? `${item.dposY * sc}px` : `${item.lineWidth * sc}px`,
                }"
              ></div>
            </template>
            <template v-else-if="item.lx === 'R'">
              <div
                :style="{
                  position: 'absolute',
                  border: `${item.lineWidth * sc}px ${item.lineStyle || 'solid'} ${item.lineColor === 0 ? 'black' : item.lineColor}`,
                  width: `${item.lrbWidth * sc}px`,
                  height: `${item.lrbHeight * sc}px`,
                }"
              ></div>
            </template>
          </div>
        </template>
      </div>
    </Form>
  </div>
</template>

<script lang="ts" setup>
  import { defineProps, reactive, watch, ref } from 'vue';
  import { Form } from 'ant-design-vue';
  import { formatNumberByFldDec } from '../../template/util';
  import { CurrencyFormatter } from '@/utils/currency';

  const FormItem = Form.Item;

  const props = defineProps<{
    items: any[];
    djInfo: any;
    formData: Record<string, any>;
    mxData?: any[]; // 新增：明细数据
  }>();

  // 创建本地的 reactive 对象
  const localFormData = reactive({ ...props.formData });
  const sc = ref(1.4); // 默认缩放比例

  // 监听 props.formData 的变化，并同步更新 localFormData
  watch(
    () => props.formData,
    (newVal) => {
      Object.assign(localFormData, newVal);
    },
    { deep: true },
  );

  // 监听本地数据变化，同步回父组件
  watch(
    localFormData,
    (newVal) => {
      Object.assign(props.formData, newVal);
    },
    { deep: true },
  );

  // 解析样式
  const parseStyle = (item: any) => {
    let className = '';
    if (item.fieldFontStyle) {
      if (item.fieldFontStyle.includes('B')) {
        className += ' font-bold';
      }
      if (item.fieldFontStyle.includes('U')) {
        className += ' underline';
      }
    }
    return className;
  };

  // 计算汇总
  const sum = (item: any, type: string) => {
    // 使用传入的明细数据，如果没有则回退到djInfo.mxTable
    const mxTable = props.mxData || props.djInfo?.mxTable || [];

    if (type === 'a') {
      // 数字汇总（类型S）
      const sumValue = mxTable.reduce((prev: any, cur: any) => {
        if (
          cur[item.fieldName] === undefined ||
          cur[item.fieldName] === null ||
          cur[item.fieldName] === ''
        ) {
          return prev;
        }
        return calculateSum(prev, cur[item.fieldName]);
      }, 0);

      // 使用字段的 fldDec 配置来格式化显示精度
      // 例如：如果 item.fldDec = 2，则显示 2 位小数；如果 item.fldDec = 0，则显示整数
      return formatNumberByFldDec(sumValue, item.fldDec);
    } else {
      // 中文汇总（类型P）
      const sumValue = mxTable.reduce((prev: any, cur: any) => {
        if (
          cur[item.fieldName] === undefined ||
          cur[item.fieldName] === null ||
          cur[item.fieldName] === ''
        ) {
          return prev;
        }
        return calculateSum(prev, cur[item.fieldName]);
      }, 0);

      // 使用字段的 fldDec 配置来格式化中文数字的精度，确保与普通数字精度一致
      return CurrencyFormatter.toChineseUppercase(sumValue, item.fldDec);
    }
  };

  const calculateSum = (prev: any, cur: any) => {
    return Number(prev) + Number(cur);
  };

  // 颜色转换
  function colorToRgba(colorValue: string) {
    const color = Number(colorValue);
    const blue = (color & 0xff0000) >> 16;
    const green = (color & 0x00ff00) >> 8;
    const red = color & 0x0000ff;
    return `rgba(${red},${green},${blue},1)`;
  }
</script>

<style lang="scss" scoped>
  :deep(.ant-input) {
    border-radius: 0;
    border-color: transparent;

    &:focus {
      border-color: #40a9ff;
      box-shadow: none;
    }
  }
</style>
