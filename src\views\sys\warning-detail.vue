<template>
  <div>
    <a-page-header :title="pageTitle" @back="handleBack" class="mb-4">
      <template #extra>
        <a-button @click="handleRefresh" :loading="loading">刷新</a-button>
      </template>
    </a-page-header>

    <a-card>
      <a-table
        :dataSource="warningDetailList"
        :columns="columns"
        :loading="loading"
        :pagination="false"
        :scroll="{ x: 1200 }"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'kcsl'">
            <span :class="{ 'text-red-500': record.kcsl <= 0 }">
              {{ record.kcsl }}
            </span>
          </template>
          <template v-else-if="column.dataIndex === 'sxrq'">
            <span :class="{ 'text-red-500': isExpiringSoon(record.sxrq) }">
              {{ record.sxrq }}
            </span>
          </template>
          <template v-else-if="column.dataIndex === 'dqts'">
            <span :class="getDqtsClass(record.dqts)"> {{ record.dqts }} 天 </span>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { message, Card as ACard, Table as ATable } from 'ant-design-vue';
  import { getWarningDetail, type WarningDetailVo } from '@/api/system';
  import { useTabs } from '@/hooks/web/useTabs';

  const route = useRoute();
  const router = useRouter();
  const { setTitle } = useTabs();

  const loading = ref(false);
  const warningDetailList = ref<WarningDetailVo[]>([]);
  const pageTitle = ref('系统预警详情');

  // 表格列定义
  const columns = [
    {
      title: '产品编号',
      dataIndex: 'dspcode',
      key: 'dspcode',
      width: 120,
      fixed: 'left',
    },
    {
      title: '产品名称',
      dataIndex: 'dspname',
      key: 'dspname',
      width: 200,
      ellipsis: true,
    },
    {
      title: '生产厂家',
      dataIndex: 'shengccj',
      key: 'shengccj',
      width: 200,
      ellipsis: true,
    },
    {
      title: '规格/型号',
      dataIndex: 'dspspgg',
      key: 'dspspgg',
      width: 150,
      ellipsis: true,
    },
    {
      title: '计量单位',
      dataIndex: 'jldw',
      key: 'jldw',
      width: 80,
    },
    {
      title: '库存数量',
      dataIndex: 'kcsl',
      key: 'kcsl',
      width: 100,
      align: 'right',
    },
    {
      title: '批号',
      dataIndex: 'miejph',
      key: 'miejph',
      width: 120,
    },
    {
      title: '有效期至',
      dataIndex: 'sxrq',
      key: 'sxrq',
      width: 100,
    },
    {
      title: '批次',
      dataIndex: 'picih',
      key: 'picih',
      width: 150,
      ellipsis: true,
    },
    {
      title: '到期天数',
      dataIndex: 'dqts',
      key: 'dqts',
      width: 100,
      align: 'right',
    },
  ];

  // 判断是否即将到期
  const isExpiringSoon = (sxrq: string) => {
    if (!sxrq) return false;
    const expireDate = new Date(sxrq);
    const today = new Date();
    const diffTime = expireDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 30; // 30天内到期显示红色
  };

  // 获取到期天数样式类
  const getDqtsClass = (dqts: number) => {
    if (dqts <= 0) return 'text-red-600 font-semibold';
    if (dqts <= 7) return 'text-red-500';
    if (dqts <= 30) return 'text-orange-500';
    return 'text-green-500';
  };

  // 获取预警详情数据
  const fetchWarningDetail = async () => {
    const gnbh = route.query.gnbh as string;
    if (!gnbh) {
      message.error('缺少功能编号参数');
      return;
    }

    // 设置页面标题和标签页标题，包含功能编号
    const titleWithGnbh = `系统预警详情 - ${gnbh}`;
    pageTitle.value = titleWithGnbh;
    setTitle(titleWithGnbh);

    loading.value = true;
    try {
      const response = await getWarningDetail(gnbh);
      warningDetailList.value = response || [];
    } catch (error) {
      console.error('获取预警详情失败:', error);
      message.error('获取预警详情失败，请稍后重试');
    } finally {
      loading.value = false;
    }
  };

  // 返回上一页
  const handleBack = () => {
    router.back();
  };

  // 刷新数据
  const handleRefresh = () => {
    fetchWarningDetail();
  };

  onMounted(() => {
    fetchWarningDetail();
  });
</script>

<style lang="scss" scoped>
  :deep(.ant-table-cell) {
    .text-red-500 {
      color: #ef4444;
    }

    .text-red-600 {
      color: #dc2626;
    }

    .text-orange-500 {
      color: #f59e0b;
    }

    .text-green-500 {
      color: #10b981;
    }
  }

  :deep(.ant-card-body) {
    padding: 0;
  }
</style>
