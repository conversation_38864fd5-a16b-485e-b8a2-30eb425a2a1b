# 系统预警功能实现说明

## 功能概述

实现了系统预警区域的点击跳转和参数传递功能，当用户点击具体的预警项目时，会跳转到详情页面并显示对应的功能编号。

## 实现的功能

### 1. 数据传递流程

- **源页面**: `src/views/home/<USER>
- **目标页面**: `src/views/sys/warning-detail.vue`
- **路由配置**: `src/router/routes/modules/home.ts`

### 2. 具体实现

#### 2.1 点击事件处理 (`handleWarningClick`)

在 `src/views/home/<USER>

```typescript
const handleWarningClick = (item: WarningInfoVo) => {
  router.push({
    path: '/home/<USER>',
    query: {
      gnbh: item.gnbh,  // 提取功能编号
    },
  });
};
```

**功能**:
- 从被点击的预警项目中提取 `gnbh`（功能编号）字段
- 通过 `router.push` 跳转到预警详情页面
- 将 `gnbh` 作为查询参数传递

#### 2.2 参数接收和标题设置

在 `src/views/sys/warning-detail.vue` 中：

```typescript
import { useTabs } from '@/hooks/web/useTabs';

const route = useRoute();
const { setTitle } = useTabs();
const pageTitle = ref('系统预警详情');

const fetchWarningDetail = async () => {
  const gnbh = route.query.gnbh as string;
  if (!gnbh) {
    message.error('缺少功能编号参数');
    return;
  }

  // 设置页面标题和标签页标题，包含功能编号
  const titleWithGnbh = `系统预警详情 - ${gnbh}`;
  pageTitle.value = titleWithGnbh;
  setTitle(titleWithGnbh);

  // ... 其他逻辑
};
```

**功能**:
- 通过 `route.query.gnbh` 接收传递的功能编号参数
- 使用 `useTabs` hook 的 `setTitle` 方法设置标签页标题
- 同时更新页面头部标题显示功能编号
- 包含参数验证和错误处理

#### 2.3 页面头部标题显示

模板中的动态标题：

```vue
<template>
  <div>
    <a-page-header :title="pageTitle" @back="handleBack" class="mb-4">
      <!-- ... -->
    </a-page-header>
    <!-- ... -->
  </div>
</template>
```

**功能**:
- 页面头部标题动态显示功能编号
- 提供更好的用户体验

### 3. 路由配置

在 `src/router/routes/modules/home.ts` 中已配置：

```typescript
{
  path: 'warning-detail',
  name: 'WarningDetail',
  component: () => import('@/views/sys/warning-detail.vue'),
  meta: {
    title: '系统预警详情',
    hideMenu: true,
  },
}
```

## 使用方式

1. 用户在首页系统预警区域点击任意预警项目
2. 系统自动提取该项目的 `gnbh` 字段
3. 跳转到预警详情页面，URL 格式为：`/home/<USER>
4. 详情页面接收参数并在标题中显示功能编号
5. 页面标题格式为：`系统预警详情 - 功能编号`

## 错误处理

- 如果缺少 `gnbh` 参数，会显示错误提示："缺少功能编号参数"
- 包含完整的异常处理机制

## 测试方式

1. 启动开发服务器：`pnpm dev`
2. 访问首页：http://localhost:5174/
3. 在系统预警区域点击任意预警项目
4. 验证页面跳转和标题显示是否正确

## 技术栈

- Vue 3 + TypeScript
- Vue Router 4
- Ant Design Vue
- Pinia (状态管理)
- useTabs Hook (标签页管理)
