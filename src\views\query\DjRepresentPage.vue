<template>
  <div class="p-4">
    <div v-if="loading" class="flex justify-center items-center h-96">
      <a-spin />
    </div>

    <div v-else class="relative bg-white" ref="printRef">
      <!-- 菜单区域 -->
      <div class="nav-menu-container" @click="handleContainerClick">
        <div class="menu-group">
          <template v-for="item in djStyle?.menuInfoList?.filter((i) => i.dsp)" :key="item.lcTitle">
            <Dropdown
              v-if="item.sonMenuList && item.sonMenuList.length > 0"
              placement="bottom"
              :trigger="['click']"
              :dropMenuList="transformMenuList(item.sonMenuList)"
              :selectedKeys="selectedKeys"
              @menu-event="handleMenuEvent"
              overlayClassName="app-locale-picker-overlay"
            >
              <a-button preIcon="ant-design:windows-outlined" type="link">
                {{ item.lcTitle }}
              </a-button>
            </Dropdown>
            <a-button
              v-else
              type="link"
              preIcon="ant-design:select-outlined"
              @click="handlerToolbar(item)"
            >
              {{ item.lcTitle }}
            </a-button>
          </template>
          <a-button type="link" preIcon="ant-design:select-outlined" @click="handleWorkflowClick">
            工作流
          </a-button>
        </div>
      </div>

      <!-- 表单区域 -->
      <div class="rounded-sm">
        <DjForm
          :items="djStyle?.kbxTableList?.filter((item) => item.upMx && item.isActive) || []"
          :djInfo="djStyle"
          v-model="formData"
          class="header-section"
        />
      </div>

      <!-- 表格区域 -->
      <div v-if="djStyle.mxActive">
        <VxeBasicTable
          ref="tableRef"
          v-bind="{
            ...gridOptions,
            height: computedTableHeight(),
          }"
          v-on="gridEvents"
        >
          <template #toolbar_buttons>
            <div class="flex items-center justify-between w-full px-2">
              <div>{{ djStyle?.runTitle }}明细</div>
            </div>
          </template>
        </VxeBasicTable>
      </div>

      <!-- 底部汇总区域 -->
      <div class="mt-4">
        <DjFooter
          :items="djStyle?.kbxTableList?.filter((item) => !item.upMx && item.isActive) || []"
          :djInfo="djStyle"
          :formData="formData"
          :mxData="djData?.mxdata || []"
          class="footer-section"
        />
      </div>
    </div>

    <!-- 底部工具栏 -->
    <div class="flex justify-end gap-2 mt-4">
      <a-button @click="handlePrint">打印</a-button>
    </div>

    <!-- 工作流弹窗 -->
    <WorkflowModal
      v-if="showWorkflowModal"
      v-model:open="showWorkflowModal"
      :danjbh="String(route.query.danjbh || '')"
      :task-id="taskId"
      :task-kind-id="taskKindId"
      :group-id="1"
      :status="String(route.query.status || '')"
      :biz-id="String(route.query.bizId || '')"
      :djlx="String(route.query.djlx || '')"
      :is-show-edit-btn="String(route.query.isShowEditBtn || '')"
      @submit="handleApprovalSubmit"
      @close="showWorkflowModal = false"
    />

    <!-- 资料详情弹窗 -->
    <ZiliaoDetailsModel
      v-if="ziliaoModalOpen"
      v-model:open="ziliaoModalOpen"
      :tableHeader="ziliaoData?.tableheader || []"
      :tableValue="ziliaoData?.tablevalue || {}"
      @cancel="() => (ziliaoModalOpen = false)"
    />
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, onUnmounted, reactive } from 'vue';
  import { getDjRepresentApi } from '@/api/template';
  import { Button as AButton, Spin as ASpin, Modal } from 'ant-design-vue';
  import { VxeBasicTable } from '@/components/VxeTable';
  import { Dropdown } from '@/components/Dropdown';
  import DjForm from './components/DjForm.vue';
  import DjFooter from './components/DjFooter.vue';
  import WorkflowModal from './components/WorkflowModal.vue';
  import ZiliaoDetailsModel from '../datacard/components/ZiliaoDetailsModel.vue';
  import { useRoute, useRouter } from 'vue-router';
  import { submitApproval } from '@/api/workflow';
  import { useWorkflowStore } from '@/store/modules/workflow';
  import { queryArchiveDataApi } from '@/api/datacard';
  import { formatNumberByFldDec } from '../template/util';
  import { CurrencyFormatter } from '@/utils/currency';

  const route = useRoute();
  const router = useRouter();
  const loading = ref(false);
  const djData = ref<Record<string, any>>({});
  const djStyle = ref<any>({});
  const formData = ref<Record<string, any>>({});
  const printRef = ref<HTMLElement | null>(null);
  const tableRef = ref();
  const taskId = ref(''); // 当前任务ID
  const taskKindId = ref(''); // 任务种类ID
  const selectedKeys = ref<string[]>([]); // 选中的菜单项

  // 资料详情相关
  const ziliaoModalOpen = ref(false);
  const ziliaoData = ref<any>(null);

  // 工作流相关
  const showWorkflowModal = ref(false);
  const workflowStore = useWorkflowStore();

  // 组件卸载时清理store数据
  onUnmounted(() => {
    workflowStore.clearCurrentWorkflowItem();
  });

  // 转换菜单列表，添加disabled属性
  function transformMenuList(menuList: any[]) {
    return menuList
      .filter((item) => item.dsp)
      .map((item) => ({
        text: item.lcTitle,
        event: item.functionName || item.lcTitle,
        disabled: false, // 启用菜单项
      }));
  }

  function handleContainerClick() {
    // 空函数，仅用于保持与render.vue的结构一致
  }

  // 处理下拉菜单事件
  function handleMenuEvent(menu: string) {
    console.log('菜单事件:', menu);

    // 根据不同的菜单事件执行不同的操作
    const menuLower = menu.toLowerCase();
    switch (menuLower) {
      case 'djpreview': // 通用打印预览
        handlePrint();
        break;
      default:
        Modal.info({
          title: '菜单事件',
          content: `功能 "${menu}" 在单据再现页面暂不可用`,
        });
        break;
    }
  }

  // 处理工具栏按钮点击
  function handlerToolbar(item: any) {
    console.log('工具栏按钮点击:', item);
    let functionName = item.functionName;

    if (functionName) {
      functionName = functionName.toLowerCase();
      // 根据不同的功能名称执行不同的操作
      switch (functionName) {
        case 'getdspinfo': // 商品详情
          handleShowProductDetail();
          break;
        case 'getddwinfo': // 单位详情
          handleShowUnitDetail();
          break;
        default:
          Modal.info({
            title: '菜单功能',
            content: `功能 "${functionName}" 在单据再现页面暂不可用`,
          });
          break;
      }
    }
  }

  // 显示商品详情
  async function handleShowProductDetail() {
    // 获取当前选中行
    const currentRow = tableRef.value?.getCurrentRecord();

    if (!currentRow || !currentRow.dspid) {
      Modal.warn({
        title: '查询错误',
        content: '未找到商品编号，请先选择商品',
        okText: '确定',
      });
      return;
    }

    try {
      // 调用档案资料查询接口
      const response = await queryArchiveDataApi({ dspid: currentRow.dspid });
      // 保存数据
      ziliaoData.value = response;
      // 打开详情弹窗
      ziliaoModalOpen.value = true;
    } catch (error) {
      console.error('商品档案查询失败:', error);
      Modal.warn({
        title: '查询错误',
        content: '商品档案查询失败，请稍后重试',
        okText: '确定',
      });
    }
  }

  // 显示单位详情
  async function handleShowUnitDetail() {
    // 从非明细项(表单数据)获取ddwid字段的值
    const ddwid = formData.value.ddwid;

    if (!ddwid) {
      Modal.warn({
        title: '查询错误',
        content: '未找到单位编号，请先选择往来单位',
        okText: '确定',
      });
      return;
    }

    try {
      // 调用档案资料查询接口
      const response = await queryArchiveDataApi({ ddwid });
      // 保存数据
      ziliaoData.value = response;
      // 打开详情弹窗
      ziliaoModalOpen.value = true;
    } catch (error) {
      console.error('往来单位档案查询失败:', error);
      Modal.warn({
        title: '查询错误',
        content: '往来单位档案查询失败，请稍后重试',
        okText: '确定',
      });
    }
  }

  // 表格配置
  const gridOptions = reactive({
    border: true,
    showOverflow: true,
    showHeaderOverflow: true,
    size: 'mini',
    stripe: true,
    rowConfig: { height: 40 },
    columnConfig: {
      resizable: true,
    },
    headerConfig: {
      height: 40,
    },
    toolbarConfig: {
      custom: false,
      export: false,
      print: false,
      zoom: false,
      refresh: false,
      slots: {
        buttons: 'toolbar_buttons',
      },
    },
    columns: [],
    // 分页配置
    pagerConfig: {
      enabled: true,
      pageSize: 50,
      pageSizes: [20, 50, 100, 200],
    },
    // 数据代理配置
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          try {
            const tableData = djData.value.mxdata || [];
            const startIndex = (page.currentPage - 1) * page.pageSize;
            const endIndex = startIndex + page.pageSize;

            // 计算表格数据的同时更新底部汇总数据
            calculateFooterData(tableData);
            return {
              items: tableData.slice(startIndex, endIndex),
              total: tableData.length,
            };
          } catch (error) {
            console.error('加载表格数据失败:', error);
            return {
              items: [],
              total: 0,
            };
          }
        },
      },
    },
  });

  function computedTableHeight() {
    const totalHeight = window.innerHeight;
    const formHeight = (djStyle.value?.tdHeight ?? 0) + 16;
    const footerHeight = djStyle.value?.qtHeight || 50;
    const availableHeight = (totalHeight - formHeight - footerHeight - 40) * 0.8;
    return Math.max(availableHeight, 400);
  }

  // 计算底部汇总数据
  function calculateFooterData(tableData: any[]) {
    if (!tableData?.length) return;
    const footerItems =
      djStyle.value?.kbxTableList?.filter((item) => !item.upMx && item.isActive) || [];
    footerItems.forEach((item) => {
      if (item.lx === 'S' || item.lx === 'P') {
        // 数字汇总
        const sum = tableData.reduce((prev, cur) => {
          const value = cur[item.fieldName];
          if (value === undefined || value === null || value === '') return prev;
          return prev + Number(value);
        }, 0);

        // 更新表单数据，应用字段的 fldDec 精度配置
        if (item.lx === 'P') {
          // 中文大写金额，使用 fldDec 配置确保精度一致
          formData.value[item.fieldName] = CurrencyFormatter.toChineseUppercase(sum, item.fldDec);
        } else {
          // 数字金额，使用 fldDec 配置格式化显示精度
          formData.value[item.fieldName] = formatNumberByFldDec(sum, item.fldDec);
        }
      }
    });
  }

  // 处理表格行点击事件
  function handleCellClick({ row }: { row: any }) {
    console.log('点击明细行数据:', row);

    if (!row || !djStyle.value?.kbxTableList) return;

    // 获取所有非明细项（表单头部和底部字段）
    const nonDetailItems = djStyle.value.kbxTableList.filter((item) => item.isActive) || [];

    // 遍历非明细项，查找与明细行数据中字段名相同的字段
    nonDetailItems.forEach((item) => {
      const fieldName = item.fieldName;

      // 如果明细行数据中存在该字段，则同步到表单数据中
      if (
        Object.prototype.hasOwnProperty.call(row, fieldName) &&
        row[fieldName] !== undefined &&
        row[fieldName] !== null
      ) {
        console.log(`同步字段 ${fieldName}: ${row[fieldName]} -> 表单数据`);

        // 根据字段类型进行适当的数据转换
        let syncValue = row[fieldName];

        // 如果是数字类型且字段类型为P（大写金额），则转换为大写，应用 fldDec 精度控制
        if (item.lx === 'P' && typeof syncValue === 'number') {
          syncValue = CurrencyFormatter.toChineseUppercase(syncValue, item.fldDec);
        }

        // 更新表单数据
        formData.value[fieldName] = syncValue;
      }
    });

    console.log('同步后的表单数据:', formData.value);
  }

  // 表格事件配置
  const gridEvents = {
    cellClick: handleCellClick,
  };

  // 组件挂载时加载数据
  onMounted(async () => {
    const danjbh = route.query.danjbh as string;
    if (danjbh) {
      await loadDjRepresentData(danjbh);
      // 从路由参数中获取taskId和taskKindId
      taskId.value = route.query.taskId as string;
      taskKindId.value = route.query.taskKindId as string;
    }
  });

  // 加载单据再现数据
  async function loadDjRepresentData(danjbh: string) {
    if (!danjbh) {
      Modal.warning({
        title: '参数错误',
        content: '单据编号不能为空',
        okText: '确定',
      });
      return;
    }

    try {
      loading.value = true;
      const response = await getDjRepresentApi(danjbh);

      // 正确处理API返回的数据结构
      const data = response?.data || response || {};

      // 设置表单数据
      djStyle.value = data.djStyle || {};
      djData.value = data.djData || {};
      formData.value = djData.value.fmxdata || {};

      // 设置表格列
      gridOptions.columns =
        djStyle.value.mxTableList
          ?.filter((item) => item.isActive)
          ?.map((item) => ({
            field: item.fieldName,
            title: item.fieldTitle,
            width: item.showLength,
            align: getColumnAlign(item.alignStyle),
            showOverflow: true,
            showHeaderOverflow: true,
          })) || [];

      // 刷新表格数据
      if (tableRef.value) {
        await tableRef.value.commitProxy('query');
      }
    } catch (error: any) {
      Modal.warn({
        title: '加载失败',
        content: error.message || '加载单据再现数据失败',
        okText: '确定',
      });
    } finally {
      loading.value = false;
    }
  }

  // 处理打印
  function handlePrint() {
    // 实现打印功能
  }

  // 获取列对齐方式
  function getColumnAlign(alignStyle: string) {
    const alignMap = {
      ALLEFT: 'left',
      ALCENTER: 'center',
      ALRIGHT: 'right',
    };
    return alignMap[alignStyle] || 'left';
  }

  // 工作流表格列定义
  // 处理工作流点击事件
  async function handleWorkflowClick() {
    showWorkflowModal.value = true;
  }

  // 处理审批提交
  async function handleApprovalSubmit(data: any) {
    try {
      await submitApproval(data);
      showWorkflowModal.value = false;
      Modal.success({
        title: '提交成功',
        content: '审批已提交成功',
        okText: '确定',
        onOk: () => {
          handleBack();
        },
      });
    } catch (error: any) {
      console.error('提交审批失败:', error);
      Modal.warn({
        title: '提交失败',
        content: '审批提交失败，请稍后重试',
        okText: '确定',
      });
    }
  }

  // 返回
  function handleBack() {
    router.back();
  }
</script>

<style lang="scss" scoped>
  .nav-menu-container {
    padding: 4px 8px;
    border-bottom: 1px solid #e8e8e8;

    .menu-group {
      display: flex;
      gap: 8px;
      align-items: center;

      :deep(.ant-btn) {
        height: 28px;
        padding: 0 8px;
        color: #000;

        &[disabled] {
          border: none;
          background: transparent;
          color: rgb(0 0 0 / 45%);
          cursor: not-allowed;
        }
      }
    }
  }

  .header-section {
    flex-shrink: 0;
    border-radius: 2px;
  }

  :deep(.flow-chart-container) {
    height: 800px !important;
  }

  :deep(.vxe-table--body-wrapper) {
    overflow: auto auto !important;
  }

  :deep(.vxe-table--header-wrapper) {
    overflow-x: hidden !important;
  }

  :deep(.vxe-body--row) {
    height: 40px !important;
  }

  :deep(.vxe-table) {
    border: 1px solid #000;

    .vxe-header--column {
      height: 40px !important;
      padding: 8px !important;
      border-color: #000;
      color: #000;
      font-weight: normal;
      line-height: 24px !important;
      white-space: nowrap !important;
    }

    .vxe-body--column {
      padding: 8px !important;
      border-color: #000;
    }
  }

  :deep(.vxe-grid) {
    padding: 0 !important;
  }

  :deep(.vxe-header--row) {
    height: 40px !important;
  }

  :deep(.ant-input) {
    border-radius: 0;
    border-color: transparent;

    &:focus {
      border-color: #40a9ff;
      box-shadow: none;
    }
  }

  :deep(.ant-btn) {
    border-radius: 0;
  }

  :deep(.ant-btn[disabled]) {
    border: none !important;
    background: transparent !important;
    color: rgb(0 0 0 / 45%) !important;
    cursor: not-allowed !important;
  }

  :deep(.workflow-tabs) {
    .ant-tabs-nav {
      margin-bottom: 16px;
    }

    .flow-chart-container {
      background-color: #fff;

      :deep(.node) {
        &.green {
          fill: #67c23a;
          color: #fff;
        }

        &.red {
          fill: #f56c6c;
          color: #fff;
        }

        &.white {
          stroke: #d9d9d9;
          fill: #fff;
          color: #000;
        }
      }

      :deep(.edge) {
        stroke-width: 1;
        stroke: #666;
      }

      :deep(.start),
      :deep(.end) {
        fill: #ff4500;
        color: #fff;
      }
    }
  }
</style>
